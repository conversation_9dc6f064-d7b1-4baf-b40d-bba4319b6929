package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

func main() {
	// 23.116672,113.375473
	asiaJakarta, _ := time.LoadLocation("Asia/Shanghai")
	jakartaSchedules, _ := prayer.Calculate(prayer.Config{
		Latitude:           23.116672,
		Longitude:          113.375473,
		Timezone:           asiaJakarta,
		TwilightConvention: prayer.Diyanet(),
		AsrConvention:      prayer.Shafii,
		PreciseToSeconds:   false,
		Corrections: prayer.ScheduleCorrections{
			Fajr:    time.Duration(0) * time.Second,
			Sunrise: time.Duration(0) * time.Second,
			Zuhr:    time.Duration(0) * time.Second,
			Asr:     time.Duration(0) * time.Second,
			Maghrib: time.Duration(0) * time.Second,
			Isha:    time.Duration(0) * time.Second,
		},
	}, 2025)

	for _, schedule := range jakartaSchedules {
		date := schedule.Date
		if strings.Contains(date, "2025-07-31") {
			fmt.Printf("%s = %v\n", date, schedule)

			// fmt.Printf("IsNormal: %v\n", schedule.IsNormal)
			fmt.Printf("Imsak: %v\n", schedule.Fajr.Add(-time.Minute*10).Format("15:04:05"))
			fmt.Printf("Fajr: %s\n", schedule.Fajr.Format("15:04:05"))
			fmt.Printf("Sunrise: %s\n", schedule.Sunrise.Format("15:04:05"))
			fmt.Printf("Zuhr: %s\n", schedule.Zuhr.Format("15:04:05"))
			fmt.Printf("Asr: %s\n", schedule.Asr.Format("15:04:05"))
			fmt.Printf("Maghrib: %s\n", schedule.Maghrib.Format("15:04:05"))
			fmt.Printf("Isha: %s\n", schedule.Isha.Format("15:04:05"))
		}
	}
}
