package main

import (
	"fmt"
	"math"
	"strings"
	"time"
)

// <PERSON><PERSON> represents different Islamic schools of thought for Asr calculation
type <PERSON><PERSON> int

const (
	<PERSON><PERSON><PERSON> = iota // Single shadow length
	Hanafi               // Double shadow length
)

// CalculationMethod represents different calculation methods
type CalculationMethod struct {
	Name         string
	FajrAngle    float64
	IshaAngle    float64
	IshaInterval int // minutes after Maghrib (0 if using angle)
}

// Common calculation methods based on Flutter code analysis
var (
	MuslimWorldLeague = CalculationMethod{"Muslim World League", 18.0, 17.0, 0}
	Egyptian          = CalculationMethod{"Egyptian", 19.5, 17.7, 0}
	Karachi           = CalculationMethod{"Karachi", 18.0, 18.0, 0}
	UmmAlQura         = CalculationMethod{"Umm Al-Qura", 18.5, 0, 90}
	Dubai             = CalculationMethod{"Dubai", 18.2, 18.2, 0}
	NorthAmerica      = CalculationMethod{"North America", 15.0, 15.0, 0}
	Kuwait            = CalculationMethod{"Kuwait", 18.0, 17.5, 0}
	Singapore         = CalculationMethod{"Singapore", 20.0, 18.0, 0}
)

// Coordinates represents geographical coordinates
type Coordinates struct {
	Latitude  float64
	Longitude float64
}

// PrayerTimes holds all prayer times for a day
type PrayerTimes struct {
	Fajr    time.Time
	Sunrise time.Time
	Dhuha   time.Time // 上午祈祷 - 日出后太阳升高4.5度
	Dhuhr   time.Time
	Asr     time.Time
	Maghrib time.Time
	Isha    time.Time
}

// SolarCalculator handles astronomical calculations with improved precision
type SolarCalculator struct {
	date        time.Time
	coordinates Coordinates
	transit     float64
	sunrise     float64
	sunset      float64
}

// NewSolarCalculator creates a new calculator
func NewSolarCalculator(date time.Time, coordinates Coordinates) *SolarCalculator {
	calc := &SolarCalculator{
		date:        date,
		coordinates: coordinates,
	}
	calc.calculate()
	return calc
}

// julianDay calculates the Julian day number
func (sc *SolarCalculator) julianDay() float64 {
	year := sc.date.Year()
	month := int(sc.date.Month())
	day := sc.date.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	return float64(int(365.25*float64(year+4716)) + int(30.6001*float64(month+1)) + day + b - 1524)
}

// calculate performs precise solar calculations based on Flutter code
func (sc *SolarCalculator) calculate() {
	jd := sc.julianDay()
	n := jd - 2451545.0

	// Mean longitude of the sun
	L := math.Mod(280.460+0.9856474*n, 360.0)

	// Mean anomaly
	g := math.Mod(357.528+0.9856003*n, 360.0)
	gRad := degToRad(g)

	// Ecliptic longitude with corrections
	lambda := math.Mod(L+1.915*math.Sin(gRad)+0.020*math.Sin(2*gRad), 360.0)
	lambdaRad := degToRad(lambda)

	// Obliquity of the ecliptic
	obliquity := 23.439 - 0.0000004*n
	obliquityRad := degToRad(obliquity)

	// Solar declination
	declination := math.Asin(math.Sin(obliquityRad) * math.Sin(lambdaRad))

	// Right ascension
	rightAscension := math.Atan2(math.Cos(obliquityRad)*math.Sin(lambdaRad), math.Cos(lambdaRad))

	// Equation of time (minutes)
	eot := 4 * (L - 0.0057183 - radToDeg(rightAscension))
	if eot > 20 {
		eot -= 1440
	} else if eot < -20 {
		eot += 1440
	}

	// Hour angle for sunrise/sunset with atmospheric refraction
	refraction := 0.833 // Standard atmospheric refraction
	cosHourAngle := (math.Sin(degToRad(-refraction)) -
		math.Sin(degToRad(sc.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(sc.coordinates.Latitude)) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		// Polar day/night
		sc.transit = 12 - sc.coordinates.Longitude/15 - eot/60
		sc.sunrise = math.NaN()
		sc.sunset = math.NaN()
		return
	}

	hourAngle := math.Acos(cosHourAngle)

	// Calculate times
	sc.transit = 12 - sc.coordinates.Longitude/15 - eot/60
	sc.sunrise = sc.transit - radToDeg(hourAngle)/15
	sc.sunset = sc.transit + radToDeg(hourAngle)/15
}

// timeForAngle calculates time when sun is at specific angle
func (sc *SolarCalculator) timeForAngle(angle float64, afterTransit bool) float64 {
	jd := sc.julianDay()
	n := jd - 2451545.0

	// Solar position calculations
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	obliquity := 23.439 - 0.0000004*n
	declination := math.Asin(math.Sin(degToRad(obliquity)) * math.Sin(degToRad(lambda)))

	rightAscension := math.Atan2(math.Cos(degToRad(obliquity))*math.Sin(degToRad(lambda)), math.Cos(degToRad(lambda)))
	eot := 4 * (L - 0.0057183 - radToDeg(rightAscension))
	if eot > 20 {
		eot -= 1440
	} else if eot < -20 {
		eot += 1440
	}

	// Hour angle calculation
	cosHourAngle := (math.Sin(degToRad(angle)) -
		math.Sin(degToRad(sc.coordinates.Latitude))*math.Sin(declination)) /
		(math.Cos(degToRad(sc.coordinates.Latitude)) * math.Cos(declination))

	if cosHourAngle < -1 || cosHourAngle > 1 {
		return math.NaN()
	}

	hourAngle := math.Acos(cosHourAngle)
	transit := 12 - sc.coordinates.Longitude/15 - eot/60

	if afterTransit {
		return transit + radToDeg(hourAngle)/15
	}
	return transit - radToDeg(hourAngle)/15
}

// asrTime calculates Asr prayer time based on shadow length
func (sc *SolarCalculator) asrTime(madhab Madhab) float64 {
	jd := sc.julianDay()
	n := jd - 2451545.0

	// Solar calculations
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := math.Mod(L+1.915*math.Sin(degToRad(g))+0.020*math.Sin(degToRad(2*g)), 360.0)

	obliquity := 23.439 - 0.0000004*n
	declination := math.Asin(math.Sin(degToRad(obliquity)) * math.Sin(degToRad(lambda)))

	// Shadow length factor
	shadowFactor := 1.0
	if madhab == Hanafi {
		shadowFactor = 2.0
	}

	// Calculate solar altitude for Asr
	latRad := degToRad(sc.coordinates.Latitude)
	asrAltitude := math.Atan(1.0 / (shadowFactor + math.Tan(math.Abs(latRad-declination))))
	asrAngle := 90 - radToDeg(asrAltitude)

	return sc.timeForAngle(-asrAngle, true)
}

// Helper functions
func degToRad(degrees float64) float64 {
	return degrees * math.Pi / 180.0
}

func radToDeg(radians float64) float64 {
	return radians * 180.0 / math.Pi
}

// FormatTime formats time in 24-hour format
func FormatTime(t time.Time) string {
	if t.IsZero() {
		return "N/A"
	}
	return t.Format("15:04")
}

// CalculatePrayerTimes calculates all prayer times with timezone support
func CalculatePrayerTimes(date time.Time, coordinates Coordinates, method CalculationMethod, madhab Madhab, timezone *time.Location) *PrayerTimes {
	// Use UTC for calculations
	utcDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC)
	calc := NewSolarCalculator(utcDate, coordinates)

	// Helper function to convert decimal hours to time.Time in specified timezone
	hoursToTime := func(hours float64) time.Time {
		if math.IsNaN(hours) {
			return time.Time{}
		}

		hours = math.Mod(hours+24, 24)
		h := int(hours)
		m := int((hours - float64(h)) * 60)
		s := int(((hours-float64(h))*60 - float64(m)) * 60)

		utcTime := time.Date(utcDate.Year(), utcDate.Month(), utcDate.Day(), h, m, s, 0, time.UTC)
		return utcTime.In(timezone)
	}

	// Calculate each prayer time
	fajrTime := calc.timeForAngle(-method.FajrAngle, false)
	sunriseTime := calc.sunrise
	dhuhaTime := calc.timeForAngle(4.5, false) // Dhuha: 4.5 degrees after sunrise
	dhuhrTime := calc.transit                  // Solar noon
	asrTime := calc.asrTime(madhab)            // Based on shadow length
	maghribTime := calc.sunset                 // Sunset

	var ishaTime float64
	if method.IshaInterval > 0 {
		ishaTime = maghribTime + float64(method.IshaInterval)/60.0
	} else {
		ishaTime = calc.timeForAngle(-method.IshaAngle, true)
	}

	return &PrayerTimes{
		Fajr:    hoursToTime(fajrTime),
		Sunrise: hoursToTime(sunriseTime),
		Dhuha:   hoursToTime(dhuhaTime),
		Dhuhr:   hoursToTime(dhuhrTime),
		Asr:     hoursToTime(asrTime),
		Maghrib: hoursToTime(maghribTime),
		Isha:    hoursToTime(ishaTime),
	}
}

func main() {
	// Get timezone from user
	// fmt.Println("请输入时区 (例如: Asia/Riyadh, Asia/Shanghai, Europe/London):")
	// fmt.Print("时区 (直接回车使用 Asia/Riyadh): ")
	// var timezoneStr string
	// fmt.Scanln(&timezoneStr)

	// if timezoneStr == "" {
	// 	timezoneStr = "Asia/Riyadh"
	// }

	timezoneStr := "Asia/Shanghai"

	timezone, err := time.LoadLocation(timezoneStr)
	if err != nil {
		fmt.Printf("无效的时区，使用默认时区 Asia/Riyadh: %v\n", err)
		timezone, _ = time.LoadLocation("Asia/Riyadh")
	}

	// Test locations with their local timezones
	locations := map[string]struct {
		coords Coordinates
		tz     string
	}{
		// "麦加 (Mecca)":       {Coordinates{21.4225, 39.8262}, "Asia/Riyadh"},
		// "开罗 (Cairo)":       {Coordinates{30.0444, 31.2357}, "Africa/Cairo"},
		// "雅加达 (Jakarta)":    {Coordinates{-6.2088, 106.8456}, "Asia/Jakarta"},
		// "伊斯坦布尔 (Istanbul)": {Coordinates{41.0082, 28.9784}, "Europe/Istanbul"},
		// "北京 (Beijing)":     {Coordinates{39.9042, 116.4074}, "Asia/Shanghai"},
		// "广州 (Guangzhou)": {Coordinates{23.116672, 113.375473}, "Asia/Shanghai"},
		"广州 (Guangzhou)": {Coordinates{23.116672, 113.375473}, "Asia/Shanghai"},
	}

	date := time.Now()
	fmt.Printf("\n计算日期: %s\n", date.Format("2006-01-02"))
	fmt.Printf("用户选择时区: %s\n", timezone.String())

	// Calculate for Mecca first
	mecca := locations["麦加 (Mecca)"]
	meccaTz, _ := time.LoadLocation(mecca.tz)

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Printf("麦加祈祷时间 (基于不同计算方法) - %s\n", mecca.tz)
	fmt.Println(strings.Repeat("=", 60))

	methods := []CalculationMethod{MuslimWorldLeague, Egyptian, Karachi, UmmAlQura}

	for _, method := range methods {
		times := CalculatePrayerTimes(date, mecca.coords, method, Shafi, meccaTz)
		fmt.Printf("\n%s:\n", method.Name)
		fmt.Printf("  Fajr (晨礼):    %s\n", FormatTime(times.Fajr))
		fmt.Printf("  Sunrise (日出): %s\n", FormatTime(times.Sunrise))
		fmt.Printf("  Dhuha (上午):   %s  (日出后4.5°)\n", FormatTime(times.Dhuha))
		fmt.Printf("  Dhuhr (晌礼):   %s\n", FormatTime(times.Dhuhr))
		fmt.Printf("  Asr (晡礼):     %s\n", FormatTime(times.Asr))
		fmt.Printf("  Maghrib (昏礼): %s\n", FormatTime(times.Maghrib))
		fmt.Printf("  Isha (宵礼):    %s", FormatTime(times.Isha))
		if method.IshaInterval > 0 {
			fmt.Printf("  (%d分钟后)", method.IshaInterval)
		}
		fmt.Println()
	}

	// Show all cities with user's selected timezone
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Printf("各城市祈祷时间 (转换为 %s 时区)\n", timezone.String())
	fmt.Println(strings.Repeat("=", 60))

	for city, info := range locations {
		times := CalculatePrayerTimes(date, info.coords, MuslimWorldLeague, Shafi, timezone)
		fmt.Printf("\n%s (%+.4f, %+.4f):\n", city, info.coords.Latitude, info.coords.Longitude)
		fmt.Printf("  Fajr %s, Sunrise %s, Dhuha %s, Dhuhr %s, Asr %s, Maghrib %s, Isha %s\n",
			FormatTime(times.Fajr), FormatTime(times.Sunrise), FormatTime(times.Dhuha),
			FormatTime(times.Dhuhr), FormatTime(times.Asr), FormatTime(times.Maghrib), FormatTime(times.Isha))
	}

	// Demonstrate Madhab difference
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("Asr时间的教法学派差异 (以麦加为例):")
	fmt.Println(strings.Repeat("=", 60))

	shafiTimes := CalculatePrayerTimes(date, mecca.coords, MuslimWorldLeague, Shafi, meccaTz)
	hanafiTimes := CalculatePrayerTimes(date, mecca.coords, MuslimWorldLeague, Hanafi, meccaTz)

	fmt.Printf("Shafi学派 (影长=物高×1):  Asr %s\n", FormatTime(shafiTimes.Asr))
	fmt.Printf("Hanafi学派 (影长=物高×2): Asr %s\n", FormatTime(hanafiTimes.Asr))

	diff := hanafiTimes.Asr.Sub(shafiTimes.Asr)
	fmt.Printf("时间差: %.0f 分钟\n", diff.Minutes())
}
